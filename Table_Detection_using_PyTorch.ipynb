pip install torch torchvision # installing pytorch


pip install 'git+https://github.com/facebookresearch/detectron2.git' # installing detector (no python libarary)

pip install opencv-python #installing opencv

pip install layoutparser # installing layoutparser

import cv2
import torch
from detectron2.config import get_cfg
from detectron2.engine import DefaultPredictor
from detectron2 import model_zoo
from detectron2.utils.visualizer import Visualizer
from detectron2.data import MetadataCatalog

# Load image
image_path = "sample.jpg"
image = cv2.imread(image_path)

# Configure Detectron2
cfg = get_cfg()
cfg.merge_from_file(model_zoo.get_config_file("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"))
cfg.MODEL.ROI_HEADS.NUM_CLASSES = 5  # PubLayNet has 5 classes
cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.5
cfg.MODEL.WEIGHTS = "https://layoutlm.blob.core.windows.net/publaynet/mask_rcnn_R_50_FPN_3x/model_final.pth"
cfg.MODEL.DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

predictor = DefaultPredictor(cfg)

# Run prediction
outputs = predictor(image)

# Extract instances
instances = outputs["instances"]
boxes = instances.pred_boxes
classes = instances.pred_classes

# Class 3 in PubLayNet is "Table"
table_boxes = boxes[classes == 3]

# Print detected table positions
for i, box in enumerate(table_boxes):
    x1, y1, x2, y2 = box.tensor.cpu().numpy()[0]
    width = x2 - x1
    height = y2 - y1
    print(f"Table {i+1}: ({x1:.0f}, {y1:.0f}) to ({x2:.0f}, {y2:.0f}) → Width: {width:.0f}, Height: {height:.0f}")
    cv2.rectangle(image, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

# Optional: visualize results
cv2.imshow("Detected Tables", image)
cv2.waitKey(0)
cv2.destroyAllWindows()


